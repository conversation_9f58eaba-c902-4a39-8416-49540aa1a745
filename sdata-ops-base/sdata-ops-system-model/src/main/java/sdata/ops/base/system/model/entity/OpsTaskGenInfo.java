package sdata.ops.base.system.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @TableName ops_task_gen_info
 */
@TableName(value = "ops_task_gen_info")
@Data
public class OpsTaskGenInfo implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 父id 默认根节点都为0
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 任务顺序编号  1-1  1-2  1-3 等
     */
    private String taskNo;


    /**
     * 任务状态  1 正常 2 异常
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer taskProcessStatus;

    /**
     * 任务完成状态 0 未完成 1 进行中 2 待复核 3 已完成  5 未发生
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer taskCompleteStatus;

    /**
     * 任务完成填写备注，如果需要
     */
    private String taskCompleteDesc;

    /**
     * 任务来源 1 手动 2 任务单元 3任务模板
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer taskRef;

    /**
     * 经办转派状态 0 未转派 1 转派
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer taskTransferStatus;

    /**
     * 复核转派状态 0 未转派 1 转派
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer taskCheckTransferStatus;


    /**
     * 实际完成操作人id
     */
    private String operationCompleteId;

    /**
     * 实际复核操作人id
     */
    private String operationCheckId;

    /**
     * 转派人id
     */
    private String taskTransferUserId;

    /**
     * 转派备注
     */
    private String taskTransferDesc;

    /**
     * 复核状态 0 未复核 1 复核通过 2 驳回
     */
    private String taskCheckStatus;

    /**
     * 复核结果备注信息
     */
    private String taskCheckDesc;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务进度
     */
    private String taskProgress;

    /**
     * 任务类型 daily 日常任务  period 周期任务 temp 临时任务
     */
    private String taskType;

    /**
     * 任务触发类型 manual 手动 daily 工作日 dynamic 自定义
     */
    private String taskTriggerType;

    /**
     * 任务触发器引用单元id
     */
    private String taskTriggerId;

    /**
     * 任务配置定时任务表达式
     */
    private String taskCronVal;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 任务完成方式（manual 手动 auto 自动)
     */
    private String taskCompleteType;

    /**
     * 任务如果为自动完成，则需要配置脚本来源，引用单元id
     */
    private String taskCompleteUnitId;

    /**
     * 任务是否需要自动稽核( 0 否  1是)
     */
    private String taskAuditType;

    /**
     * 如果需要自动稽核，引用脚本来源id
     */
    private String taskAuditUnitId;

    /**
     * 告警通知方式 1 站内工作台 2 邮件
     */
    private String taskWarnNotice;

    /**
     * 任务优先级 （1 高 2 中 3低）
     */
    private String taskPriority;

    /**
     * 任务紧急程度(1紧急 2 一般 3 普通)
     */
    private String taskLevel;

    /**
     * 任务是否需要附件（0否1 是）
     */
    private String taskAttachmentsType;

    /**
     * 任务归属是谁 1 岗位 2 具体人员
     */
    private String taskOwnerType;

    /**
     * 任务归属id ，岗位id或者人员id
     */
    private String taskOwnerId;

    /**
     * 任务归属真实值，冗余字段
     */
    private String taskOwnerVal;

    /**
     * 任务是否需要复核 （0否1 是）
     */
    private String taskCheckReq;

    /**
     * 复核权限对象类型(1岗位2人员)
     */
    private String taskCheckType;

    /**
     * 复核权限对象id
     */
    private String taskCheckId;

    /**
     * 复核权限对象真实值，冗余字段
     */
    private String taskCheckVal;


    /**
     * 生成日期过
     */
    private String taskGenTime;
    /**
     * 任务开始时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskStartTime;

    /**
     * 任务结束时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date taskEndTime;


    //开始时间规则
    private int taskStartThreshold;
    //结束时间规则
    private int taskEndThreshold;
    /**
     * 任务属性标签
     */
    private String taskTags;

    /**
     * 任务权限类型(1到机构 2 部门 3 岗位 4 具体人员)
     */
    private String taskAuthType;

    /**
     * 任务权限归属id
     */
    private String taskAuthId;

    /**
     * 任务权限归属真实值，冗余字段
     */
    private String taskAuthVal;

    /**
     * 任务范围(
     * QUERY 查询权限,
     * DOWN 下载权限,
     * EDIT 编辑权限,
     * TRANSFER,转派
     * INVALID,作废
     * AUDIT,复核
     * ,ALL所有)
     */
    private String taskAuthScope;

    /**
     * 所有子级id
     */
    private String taskChildIds;


    /**
     * 当前节点的所有上级节点
     */
    private String parentIds;


    /**
     * 当前节点的同级节点标记id
     */
    private String floorId;

    /**
     * 任务是否可以顺延,T日任务，完成时间可以调整  "0" 否 "1" 是
     */
    private String taskDeferredType;

    /**
     * 顺延阈值  加几个工作日
     */
    private Integer taskDeferredCount;

    /**
     * 任务点击完成前需要依赖前置节点是否已经完成
     */
    private String dependOnIds;


    /**
     * 任务点击完成时,需要判定任务的自身属性要素已经点击填充
     */
    private String requiredItem;


    /**
     * 任务生成的来源   任务单元id
     * 校验功能需要，目前设计
     */
    private String taskRefId;

    /**
     * 冗余归属岗位id
     */
    private String ownerOrgId;

    /**
     * 冗余复核岗位id
     */
    private String checkOrgId;

    /**
     * 任务绑定模板id
     */
    private String taskBindTemplateId;

    /**
     * 按照模板排序展示,多模板组合模板id排序
     */
    private int taskSort;


    /**
     * 在最低权限下，该条数据是否全部可见，默认不可见 0 可见 1
     */
    private int accessLevel;


    /**
     * 工作量计数
     */
    private int workAmount;

    /**
     * 工作量计数开关
     */
    private int workAmountFlag;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT, insertStrategy = FieldStrategy.IGNORED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, insertStrategy = FieldStrategy.IGNORED)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 完成时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;


    /**
     * 复核通过时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /**
     * 删除标记
     */
    private String deleted;

    /**
     * 是否是延期完成 默认0 是正常完成 1 是延期完成
     */
    private String delayState;

    @TableField(exist = false)
    private List<OpsTaskGenInfo> children;

    @TableField(exist = false)
    @JsonIgnore
    private List<Long> setIds;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 是否包含导入明显 1、是 0、否
     */
    @TableField(exist = false)
    private Integer importStatus = 0;

    /**
     * 明显总数和完成数
     */
    @TableField(exist = false)
    private long totalDetail;

    /**
     * 任务明细关联的指标ID，与指标中心一对一绑定
     */
    private String indicatorId;
    @TableField(exist = false)
    private long completeDetail;


    /**
     * 任务名称追加 默认 0 不追加 1 追加
     */
    private int taskNameAppend;

    /**
     * 任务名称追加类型  默认0 缺省值  1 年 2 季度 3 月 4 周
     */
    private int taskAppendType;

    /**
     * 任务创建类型 默认0  update  1 insert
     */
    private int taskCreateType;

    /**
     * 数据展示类型（经办或者复核）1:经办（默认） 2:复核
     */
    @TableField(exist = false)
    private int operationDisplayType = 1;
}