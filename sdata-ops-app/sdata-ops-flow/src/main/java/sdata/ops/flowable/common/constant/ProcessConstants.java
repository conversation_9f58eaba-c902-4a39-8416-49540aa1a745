package sdata.ops.flowable.common.constant;

/**
 * 流程常量信息
 *
 * <AUTHOR>
 * @date 2021/4/17 22:46
 */
public interface ProcessConstants {

    String SUFFIX = ".bpmn";

    /**
     * 动态数据
     */
    String DATA_TYPE = "dynamic";

    /**
     * 单个审批人
     */
    String USER_TYPE_ASSIGNEE = "assignee";


    /**
     * 候选人
     */
    String USER_TYPE_USERS = "candidateUsers";


    /**
     * 审批组
     */
    String USER_TYPE_ROUPS = "candidateGroups";

    /**
     * 单个审批人
     */
    String PROCESS_APPROVAL = "approval";

    /**
     * 会签人员
     */
    String PROCESS_MULTI_INSTANCE_USER = "userList";

    /**
     * nameapace
     */
    String NAMASPASE = "http://flowable.org/bpmn";

    /**
     * 会签节点
     */
    String PROCESS_MULTI_INSTANCE = "multiInstance";

    /**
     * 自定义属性 dataType
     */
    String PROCESS_CUSTOM_DATA_TYPE = "dataType";

    /**
     * 自定义属性 userType
     */
    String PROCESS_CUSTOM_USER_TYPE = "userType";

    /**
     * 自定义属性 localScope
     */
    String PROCESS_FORM_LOCAL_SCOPE = "localScope";

    /**
     * 自定义属性 流程状态
     */
    String PROCESS_STATUS_KEY = "processStatus";
    /**
     * 流程发起人名称
     */
    String INITIATOR_NAME = "initiatorName";
    String INITIATOR_ROLE = "initiatorRole";
    String INITIATOR_DEPT = "initiatorDept";
    String INITIATOR_EMAIL = "initiatorEmail";
    String INITIATOR_PHONE = "initiatorPhone";


    /**
     * 流程跳过
     */
    String FLOWABLE_SKIP_EXPRESSION_ENABLED = "_FLOWABLE_SKIP_EXPRESSION_ENABLED";

    /**
     * 发起流程时的流程名字
     */
    String PROCESS_INSTANCE_NAME_FIELD = "titleCustom";

}
