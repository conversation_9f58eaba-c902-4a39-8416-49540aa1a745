package sdata.ops.system.controller;


import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import sdata.ops.common.core.annotation.ControllerAuditLog;
import sdata.ops.common.enums.ModuleName;
import sdata.ops.common.enums.OperateType;
import sdata.ops.base.system.model.dto.ConditionTaskDTO;
import sdata.ops.base.system.model.entity.OpsTaskAttrBasic;
import sdata.ops.base.system.model.entity.OpsTaskAttrBasicReplica;
import sdata.ops.base.system.model.entity.OpsTaskGenInfo;
import sdata.ops.base.system.model.entity.OpsTaskTemplate;
import sdata.ops.base.system.model.vo.TaskCheckVO;
import sdata.ops.base.system.model.vo.TaskCompleteVO;
import sdata.ops.base.system.model.vo.TaskTransferVO;
import sdata.ops.base.system.model.vo.TemplateVO;
import sdata.ops.common.api.MessageConstant;
import sdata.ops.common.api.R;
import sdata.ops.common.core.util.SecureUtil;
import sdata.ops.system.job.TaskException;
import sdata.ops.system.service.OpsTaskAttrBasicReplicaService;
import sdata.ops.system.service.OpsTaskAttrBasicService;
import sdata.ops.system.service.OpsTaskGenInfoService;
import sdata.ops.system.service.OpsTaskTemplateService;
import sdata.ops.system.service.WorkflowTriggerScheduleService;
import sdata.ops.indicator.api.feign.IndicatorInfoFeign;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import java.util.HashMap;
import java.util.Map;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务中心接口
 * 包括任务最小单元配置，任务查询，任务触发
 */
@RestController
@RequestMapping("/taskCenter")
@RequiredArgsConstructor
@Slf4j
public class TasksCenterController {


    /**
     * 生成任务列表服务类
     */
    private final OpsTaskGenInfoService opsTaskGenInfoService;

    private final OpsTaskAttrBasicService opsTaskAttrBasicService;

    private final OpsTaskTemplateService taskTemplateService;

    private final OpsTaskAttrBasicReplicaService replicaService;

    private final WorkflowTriggerScheduleService workflowTriggerScheduleService;

    private final IndicatorInfoFeign indicatorInfoFeign;


    /**
     * 获取与当前用户相关的任务列表
     *
     * @return list
     */
    @GetMapping("/list")
    @ControllerAuditLog(value = "获取与当前用户相关的任务列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    public R<Object> taskList() {
        return null;
    }


    @PostMapping("/createByTemp")
    @ControllerAuditLog(value = "通过模板创建任务", operateType = OperateType.INSERT, moduleName = ModuleName.SYSTEM)
    public R<Object> createByTemp(@RequestBody OpsTaskAttrBasic vo) {
        if (Objects.nonNull(vo)) {
            OpsTaskGenInfo info = opsTaskGenInfoService.convertGenInfoByTemp(vo);
            opsTaskGenInfoService.createSingleTask(info);
        }
        return R.success(MessageConstant.ADD_SUCCESS);
    }

    /**
     * 获取与用户相关的任务列，分页形式
     *
     * @return IPage
     */
    @GetMapping("/listByPage")
    @ControllerAuditLog(value = "获取与用户相关的任务列，分页形式", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    public R<Object> taskListByPage(@RequestParam(value = "taskName", required = false) String taskName,
                                    @RequestParam(value = "taskStatus", required = false) String taskStatus,
                                    @RequestParam(value = "taskType", required = false) String taskType,
                                    @RequestParam(value = "orgId", required = false) String orgId,
                                    @RequestParam(value = "createTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date createTime,
                                    @RequestParam(value = "page", required = false, defaultValue = "1") int page,
                                    @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = listWrapper(taskName, taskStatus, taskType, createTime);
        //默认第一次分页查都为根节点任务,兼容模板类型任务展示
        queryWrapper.eq(OpsTaskGenInfo::getParentId, 0);
        Page<OpsTaskGenInfo> pageOc = new Page<>(page, pageSize);
        //附加权限查询生成
        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(orgId);
        //如果条件附加为null则组织权限未配置
        if (Objects.isNull(condition)) {
            return R.data(new Page<OpsTaskGenInfo>(1, 10));
        }
        //主日常任务查询，每个交易日的
        //特殊任务 - 延期的
        //特殊任务 - 周期轮转的
        //authFilter 权限过滤
        IPage<OpsTaskGenInfo> res = opsTaskGenInfoService.pageCustom(pageOc, queryWrapper, condition);
        //查询该节点内所有有子集节点任务清单，并树状组织,赋值给当前分页数据
        Map<Long, String> p_child = new HashMap<>();
        for (OpsTaskGenInfo info : res.getRecords()) {
            if (StringUtils.hasText(info.getTaskChildIds())) {
                p_child.put(info.getId(), info.getTaskChildIds());
            }
        }
        //排序
        res.getRecords().sort(Comparator.comparing(OpsTaskGenInfo::getTaskSort, Comparator.nullsLast(Comparator.naturalOrder()))
                .thenComparing(OpsTaskGenInfo::getTaskBindTemplateId, Comparator.nullsLast(Comparator.naturalOrder())));
        if (!p_child.isEmpty()) {
            //查询当前任务中有子任务的清单项目，并重新赋值
            Map<Long, List<OpsTaskGenInfo>> child = opsTaskGenInfoService.findChild(p_child, condition);
            //authFilter 权限过滤
            for (OpsTaskGenInfo record : res.getRecords()) {
                if (child.containsKey(record.getId())) {
                    record.setChildren(child.get(record.getId()));
                }
            }
        }
        return R.data(res);
    }


    @GetMapping("/tempDetail")
    @ControllerAuditLog(value = "获取模板任务详情", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    public R<Object> tempTaskDetail(@RequestParam("id") String id) {
        OpsTaskGenInfo pInfo = opsTaskGenInfoService.getById(id);
        if (StringUtils.hasText(pInfo.getTaskChildIds())) {
            ConditionTaskDTO conditionTaskDTO = new ConditionTaskDTO();
            conditionTaskDTO.setType(4);
            Map<Long, List<OpsTaskGenInfo>> childInfos = opsTaskGenInfoService.findChild(Map.of(pInfo.getId(), pInfo.getTaskChildIds()), conditionTaskDTO);
            pInfo.setChildren(childInfos.get(pInfo.getId()));
        }
        return R.data(pInfo);
    }

    /**
     * 单个详情获取
     *
     * @param id 任务清单项id
     * @return single
     */
    @GetMapping("/getById")
    @ControllerAuditLog(value = "单个详情获取", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    public R<Object> taskInfoGetById(@RequestParam("id") String id) {
        return R.data(opsTaskGenInfoService.getById(id));
    }


    /**
     * 任务生成  通过任务单元
     *
     * @return str
     */
    @PostMapping("/createByUnit")
    @ControllerAuditLog(value = "任务生成 通过任务单元", operateType = OperateType.INSERT, moduleName = ModuleName.SYSTEM)
    public R<Object> taskCreateByUnit(@RequestBody OpsTaskAttrBasic vo) {
        if (Objects.nonNull(vo)) {
            Long id = vo.getId();
            OpsTaskGenInfo info = opsTaskGenInfoService.convertGenInfo(vo);
            opsTaskGenInfoService.createSingleTask(info, String.valueOf(id));
        }
        return R.success(MessageConstant.ADD_SUCCESS);
    }

    /**
     * 任务生成  通过任务单元id
     *
     * @return str
     */
    @GetMapping("/createByUnitId")
    @ControllerAuditLog(value = "任务生成 通过任务单元id", operateType = OperateType.INSERT, moduleName = ModuleName.SYSTEM)
    public R<Object> taskCreateByUnitId(@RequestParam("id") String id) {
        OpsTaskAttrBasic vo = opsTaskAttrBasicService.getById(Long.parseLong(id));
        if (Objects.nonNull(vo)) {
            OpsTaskGenInfo info = opsTaskGenInfoService.convertGenInfo(vo);
            opsTaskGenInfoService.createSingleTask(info, id);
        }
        return R.success(MessageConstant.ADD_SUCCESS);
    }


    /**
     * 任务生成通过模板
     *
     * @param vo 请求内容
     * @return bool
     */
    @PostMapping("/createByTemplate")
    @ControllerAuditLog(value = "任务生成通过模板", operateType = OperateType.INSERT, moduleName = ModuleName.SYSTEM)
    public R<Object> createTaskByTemplate(@RequestBody TemplateVO vo) {
        opsTaskGenInfoService.createTaskByTemplate(vo, false);
        return R.success(MessageConstant.ADD_SUCCESS);
    }

    /**
     * 任务生成通过模板
     *
     * @param id 模板id
     * @return bool
     */
    @GetMapping("/createByTemplateId")
    @ControllerAuditLog(value = "任务生成通过模板", operateType = OperateType.INSERT, moduleName = ModuleName.SYSTEM)
    public R<Object> createByTemplateId(@RequestParam("id") String id) {
        TemplateVO vo = new TemplateVO();
        OpsTaskTemplate template = taskTemplateService.getById(id);
        List<OpsTaskAttrBasicReplica> res = replicaService.viewList(id);
        vo.setTemplate(template);
        vo.setList(res);
        opsTaskGenInfoService.createTaskByTemplate(vo, false);
        return R.success(MessageConstant.ADD_SUCCESS);
    }

    /**
     * 任务重置，无论任务状态是 未发生，已完成换是待复核，都重置为进行中
     * 需要向上查询其父与根的状态是否同步回滚
     * @param id  任务id
     * @return  bool
     */
    @GetMapping("/taskReset")
    @ControllerAuditLog(value = "任务重置", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> taskReset(@RequestParam("id")String id){

        opsTaskGenInfoService.taskReset(id);

        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    /**
     * 任务置完成
     *
     * @param vo 请求内容
     * @return bool
     */
    @PostMapping("/taskComplete")
    @ControllerAuditLog(value = "任务置完成", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> taskComplete(@RequestBody TaskCompleteVO vo) {
        //检查必填项是否完成,目前必填项目场景之一是附件要有，且同一人覆盖，不同人保留
        //检查完成是否依赖其他任务,如果需要则检查其他任务状态
        //最后置完成
        String type = opsTaskGenInfoService.taskComplete(vo);
        if (type.equals("1")) {
            return R.fail("本任务附件未上传,不能操作");
        }
        if (type.equals("2")) {
            return R.fail("本任务依赖前置任务还未完成,不能操作");
        }
        if (type.equals("4")) {
            return R.fail("不允许批量完成");
        }
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }


    /**
     * 任务置未发生
     *
     * @param vo 请求内容
     * @return bool
     */
    @PostMapping("/taskNoExist")
    @ControllerAuditLog(value = "任务置未发生", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> taskNoExist(@RequestBody TaskCompleteVO vo) {
        //检查当前任务id是单个应用还是包含下级节点
        //最后未发生
        opsTaskGenInfoService.taskNoExist(vo);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    private LambdaQueryWrapper<OpsTaskGenInfo> listWrapper(String taskName, String taskStatus, String taskType, Date createTime) {
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(taskName), OpsTaskGenInfo::getTaskName, taskName);
        queryWrapper.eq(StringUtils.hasText(taskStatus), OpsTaskGenInfo::getTaskProcessStatus, taskStatus);
        queryWrapper.eq(StringUtils.hasText(taskType), OpsTaskGenInfo::getTaskType, taskType);
        queryWrapper.eq(Objects.nonNull(createTime), OpsTaskGenInfo::getTaskGenTime, DateUtil.format(createTime, "yyyy-MM-dd"));
        queryWrapper.orderByDesc(OpsTaskGenInfo::getCreateTime);
        queryWrapper.eq(OpsTaskGenInfo::getDeleted, "0");
        return queryWrapper;
    }

    /**
     * 待复核列表
     *
     * @return bool
     */
    @GetMapping("/auditList")
    @ControllerAuditLog(value = "待复核列表", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    public R<Object> auditList(@RequestParam(value = "taskName", required = false) String taskName,
                               @RequestParam(value = "taskStatus", required = false) String taskStatus,
                               @RequestParam(value = "taskType", required = false) String taskType,
                               @RequestParam(value = "orgId", required = false) String orgId,
                               @RequestParam(value = "createTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date createTime,
                               @RequestParam(value = "page", required = false, defaultValue = "1") int page,
                               @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize) {
        //待复核列表查询接口——
        //当前人权限内所有岗位,然后对任务清单中所有完成状态为2的且，复核类型是岗位或者指定人是当前用户的
        LambdaQueryWrapper<OpsTaskGenInfo> wrapper = listWrapper(taskName, taskStatus, taskType, createTime);
        wrapper.eq(OpsTaskGenInfo::getTaskCompleteStatus,2);
        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(null);
        if (Objects.isNull(condition)) {
            return R.data(new Page<OpsTaskGenInfo>(page, pageSize));
        }
        //先查询分页的根节点条目
        List<OpsTaskGenInfo> result = opsTaskGenInfoService.listCustomAudit(wrapper, condition);
        return R.data(result);
    }


    /**
     * 任务转派接口
     *
     * @param transferVO 转派内容
     * @return msg
     */
    @PostMapping("/taskTransfer")
    @ControllerAuditLog(value = "任务转派接口", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> transferTask(@RequestBody TaskTransferVO transferVO) {

        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(null);
        if (Objects.isNull(condition)) {
            return R.fail("权限异常");
        }
        //如果不是部门领导且非管理员，则权限不足
//        if (condition.getType() != 2 && !SecureUtil.isAdmin(null)) {
//            return R.fail("权限不足");
//        }
        opsTaskGenInfoService.transferTask(transferVO);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    /**
     * 任务批量转派接口
     *
     * @param transferVO 转派id
     * @return msg
     */
    @PostMapping("/batchTaskTransfer")
    @ControllerAuditLog(value = "任务批量转派接口", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> batchTransferTask(@RequestBody TaskTransferVO transferVO) {

        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(null);
        if (Objects.isNull(condition)) {
            return R.fail("权限异常");
        }
        //如果不是部门领导且非管理员，则权限不足
        if (condition.getType() != 2 && !SecureUtil.isAdmin(null)) {
            return R.fail("权限不足");
        }
        opsTaskGenInfoService.batchTransferTask(transferVO);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }


    /**
     * 任务复核接口
     *
     * @param transferVO 转派内容
     * @return msg
     */
    @PostMapping("/taskAudit")
    @ControllerAuditLog(value = "任务复核接口", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> auditTask(@RequestBody TaskCheckVO transferVO) {

        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(null);
        if (Objects.isNull(condition)) {
            return R.fail("权限异常");
        }
        //如果岗位信息为空且非管理员，则权限不足
        if (condition.getPostIds().isEmpty() && !SecureUtil.isAdmin(null)) {
            return R.fail("权限不足");
        }
        transferVO.setUserName(SecureUtil.currentUserName()).setUserId(SecureUtil.currentUserId());
        opsTaskGenInfoService.reviewTaskProcess(condition, transferVO);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }


    /**
     * 任务复核接口-批量
     *
     * @param checkVO;
     * @return msg
     */
    @PostMapping("/batchTaskAudit")
    @ControllerAuditLog(value = "任务复核接口-批量", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> batchAuditTask(@RequestBody TaskCheckVO checkVO) {

        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(null);
        if (Objects.isNull(condition)) {
            return R.fail("权限异常");
        }
        //如果岗位信息为空且非管理员，则权限不足
        if (condition.getPostIds().isEmpty() && !SecureUtil.isAdmin(null)) {
            return R.fail("权限不足");
        }
        checkVO.setUserName(SecureUtil.currentUserName()).setUserId(SecureUtil.currentUserId());
        opsTaskGenInfoService.batchReviewTaskProcess(condition, checkVO);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }


    /**
     * 历史完成备注信息,默认只查近一年
     *
     * @return list
     */
    @GetMapping("/taskHisCompleteDesc")
    @ControllerAuditLog(value = "历史完成备注信息", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    public R<Object> hisComplete(@RequestParam(value = "refId", required = false) String refId) {
        if (!StringUtils.hasText(refId)) {
            return R.data(new ArrayList<>());
        }
        Date lastTime = DateUtil.offsetMonth(new Date(), -12);
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OpsTaskGenInfo::getTaskCompleteDesc);
        queryWrapper.ge(OpsTaskGenInfo::getCreateTime, lastTime);
        queryWrapper.ge(OpsTaskGenInfo::getCreateTime, new Date());
        queryWrapper.eq(OpsTaskGenInfo::getTaskRefId, refId);
        queryWrapper.orderByDesc(OpsTaskGenInfo::getCreateTime);
        List<OpsTaskGenInfo> ls = opsTaskGenInfoService.list(queryWrapper);
        List<String> res = ls.stream().map(OpsTaskGenInfo::getTaskCompleteDesc).distinct().collect(Collectors.toList());
        return R.data(res);
    }

    /**
     * 历史复核备注信息，默认只查近一年
     *
     * @return
     */
    @GetMapping("/taskHisCheckDesc")
    @ControllerAuditLog(value = "历史复核备注信息", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    public R<Object> hisCheckDesc(@RequestParam("refId") String refId) {
        Date lastTime = DateUtil.offsetMonth(new Date(), -12);
        LambdaQueryWrapper<OpsTaskGenInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OpsTaskGenInfo::getTaskCheckDesc);
        queryWrapper.ge(OpsTaskGenInfo::getCreateTime, lastTime);
        queryWrapper.ge(OpsTaskGenInfo::getCreateTime, new Date());
        queryWrapper.eq(OpsTaskGenInfo::getTaskRefId, refId);
        queryWrapper.orderByDesc(OpsTaskGenInfo::getCreateTime);
        List<OpsTaskGenInfo> ls = opsTaskGenInfoService.list(queryWrapper);
        List<String> res = ls.stream().map(OpsTaskGenInfo::getTaskCheckDesc).distinct().collect(Collectors.toList());
        return R.data(res);
    }

    @GetMapping("/dailTaskExecute")
    @ControllerAuditLog(value = "测试执行每日任务", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> testExecuteDaily() {
        opsTaskGenInfoService.schedulerProcessByDaily();
        return R.success("once process success");
    }

    /**
     * 任务清单说明修改,并同步修改来源任务单元
     *
     * @param info body
     * @return str
     */
    @PostMapping("/rewriteTaskDesc")
    @Transactional(rollbackFor = Exception.class)
    @ControllerAuditLog(value = "任务清单说明修改", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> rewriteTaskDesc(@RequestBody OpsTaskGenInfo info) {
        if (!StringUtils.hasText(info.getTaskDesc())) {
            return R.success(MessageConstant.SAVE_SUCCESS);
        }
        //先更新任务清单的desc说明
        LambdaUpdateWrapper<OpsTaskGenInfo> infoUpdate = new LambdaUpdateWrapper<>();
        infoUpdate.set(OpsTaskGenInfo::getTaskDesc, info.getTaskDesc());
        infoUpdate.eq(OpsTaskGenInfo::getId, info.getId());
        opsTaskGenInfoService.update(infoUpdate);
        if (StringUtils.hasText(info.getTaskBindTemplateId())) {
            //更新replica表
            LambdaUpdateWrapper<OpsTaskAttrBasicReplica> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OpsTaskAttrBasicReplica::getTaskDesc, info.getTaskDesc());
            updateWrapper.eq(OpsTaskAttrBasicReplica::getId, info.getTaskRefId());
            replicaService.update(updateWrapper);
        } else {
            //更新ATTR表
            LambdaUpdateWrapper<OpsTaskAttrBasic> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(OpsTaskAttrBasic::getTaskDesc, info.getTaskDesc());
            updateWrapper.eq(OpsTaskAttrBasic::getId, info.getTaskRefId());
            opsTaskAttrBasicService.update(updateWrapper);
        }
        return R.success(MessageConstant.SAVE_SUCCESS);
    }

    /**
     * 明细任务第三方抓取数据源不能处理信息查询
     */
    @GetMapping("/thirdList")
    @ControllerAuditLog(value = "明细任务第三方抓取数据源不能处理信息查询", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    public R<Object> specTaskUnProcessList(){
        String userId=SecureUtil.currentUserId();
        return R.data(replicaService.queryUnProcessDataByUserId(userId)) ;
    }

    /**
     * 明细任务第三方抓取数据源不能处理信息查询
     */
    @PostMapping("/assignTask")
    @ControllerAuditLog(value = "明细任务第三方抓取数据源不能处理信息分配", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> assignTask(@RequestParam("dataId")String dataId,@RequestParam("taskId")String taskId,@RequestParam(value = "triggerId",required = false,defaultValue = "-1")String triggerId) throws TaskException {
        //先标记分配到对应明细表
        //再执行时间逻辑
        //再标记该内容已经被处理
        String userId=SecureUtil.currentUserId();
        replicaService.modifySpecTaskInfo(userId,dataId,taskId,triggerId);
        return R.success("操作完成") ;
    }

    /**
     * 更新单条任务截止时间
     */
    @PostMapping("/editTaskEndTime")
    @ControllerAuditLog(value = "更新单条任务截止时间", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<Object> editTaskEndTime(@RequestBody OpsTaskGenInfo opsTaskGenInfo) {

        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(null);
        if (Objects.isNull(condition)) {
            return R.fail("权限异常");
        }
        //如果岗位信息为空且非管理员，则权限不足
        if (condition.getPostIds().isEmpty() && !SecureUtil.isAdmin(null)) {
            return R.fail("权限不足");
        }
        LambdaUpdateWrapper<OpsTaskGenInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OpsTaskGenInfo::getId, opsTaskGenInfo.getId())
                .set(OpsTaskGenInfo::getTaskEndTime, opsTaskGenInfo.getTaskEndTime());
        opsTaskGenInfoService.update(updateWrapper);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    /**
     * 删除任务
     */
    @GetMapping("/deleteTask")
    @ControllerAuditLog(value = "删除任务", operateType = OperateType.DELETE, moduleName = ModuleName.SYSTEM)
    public R<Object> deleteTask(@RequestParam("id") String id) {

        ConditionTaskDTO condition = opsTaskGenInfoService.taskSpecialAuthFilter(null);
        if (Objects.isNull(condition)) {
            return R.fail("权限异常");
        }
        //如果岗位信息为空且非管理员，则权限不足
        if (condition.getPostIds().isEmpty() && !SecureUtil.isAdmin(null)) {
            return R.fail("权限不足");
        }
        List<String> list = new ArrayList<>();
        list.add(id);
        OpsTaskGenInfo opsTaskGenInfo = opsTaskGenInfoService.getById(id);
        if(opsTaskGenInfo.getTaskChildIds() != null){
            list.addAll(Arrays.asList(opsTaskGenInfo.getTaskChildIds().split(",")));
        }
        opsTaskGenInfoService.removeBatchByIds(list);
        return R.success(MessageConstant.OPERATOR_SUCCESS);
    }

    // ========== 工作流触发器相关接口 ==========

    /**
     * 工作流触发器主调度任务
     * 6-22点每半小时执行一次
     * 轮询所有配置了工作流绑定的任务单元，根据调度频率决定是否调用工作流
     */
    @GetMapping("/executeWorkflowTriggers")
    @ControllerAuditLog(value = "工作流触发器主调度任务", operateType = OperateType.UPDATE, moduleName = ModuleName.SYSTEM)
    public R<String> executeWorkflowTriggers() {
        try {
            String result = workflowTriggerScheduleService.executeWorkflowTriggers();
            return R.success(result);
        } catch (Exception e) {
            log.error("工作流触发器主调度任务执行异常", e);
            return R.fail("工作流触发器主调度任务执行失败: " + e.getMessage());
        }
    }

    /**
     * 任务指标查询接口
     * 根据任务ID查询关联的指标并执行，返回指标执行结果
     *
     * @param taskId 任务ID
     * @return 指标执行结果
     */
    @GetMapping("/indicator/query")
    @ControllerAuditLog(value = "任务指标查询", operateType = OperateType.QUERY, moduleName = ModuleName.SYSTEM)
    public R<Object> queryTaskIndicator(@RequestParam("taskId") Long taskId) {
        try {
            // 1. 根据taskId查询任务信息，获取indicatorId
            OpsTaskGenInfo taskInfo = opsTaskGenInfoService.getById(taskId);
            if (taskInfo == null) {
                return R.fail("任务不存在");
            }

            String indicatorId = taskInfo.getIndicatorId();
            if (!StringUtils.hasText(indicatorId)) {
                return R.fail("该任务未绑定指标");
            }

            // 2. 构建指标执行参数
            Map<String, Object> input = new HashMap<>();
            input.put("taskId", taskId);
            input.put("indicatorId", indicatorId);

            // 3. 通过Feign远程调用指标服务执行指标
            R<Object> indicatorResult = indicatorInfoFeign.executeMetric(indicatorId, input);

            if (indicatorResult == null || !indicatorResult.isSuccess()) {
                return R.fail("指标执行失败: " + (indicatorResult != null ? indicatorResult.getMessage() : "未知错误"));
            }

            return indicatorResult;

        } catch (Exception e) {
            log.error("任务指标执行异常，taskId: {}", taskId, e);
            return R.fail("指标执行失败: " + e.getMessage());
        }
    }

}
